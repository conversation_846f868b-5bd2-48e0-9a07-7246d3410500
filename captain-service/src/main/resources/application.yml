server:
  port: 9095

spring:
  application:
    name: captain-service

  # 数据库配置
  datasource:
    url: *********************************************************************************************************************************
    username: lihui
    password: <PERSON><PERSON>@123!
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5

  # Flyway配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 0
    validate-on-migrate: true
    out-of-order: false

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/
    # 启用健康检查
    healthcheck:
      enabled: true
    # 增加重试机制
    registry-fetch-interval-seconds: 5
    initial-instance-info-replication-interval-seconds: 10
    instance-info-replication-interval-seconds: 10
    eureka-service-url-poll-interval-seconds: 5
  instance:
    # 增加心跳机制
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 15
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}

# WebSocket服务配置
websocket:
  service:
    url: ws://localhost:8084/ws/notification
    # 重连配置
    reconnect:
      max-attempts: 10
      delay: 5000

# OpenAI配置
openai:
  api-key: ${OPENAI_API_KEY:}  # 从环境变量获取API密钥
  model: gpt-3.5-turbo         # 使用的模型
  timeout: 30                  # 超时时间（秒）

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  # 详细的Micrometer Tracing配置
  tracing:
    enabled: true
    sampling:
      probability: 1.0  # 100% sampling for development
    propagation:
      type: B3_MULTI  # 使用B3多头格式，兼容性更好
    brave:
      enabled: true
      span-joining-enabled: true
      propagation:
        enabled: true
        type: B3_MULTI
  # 启用观察
  observations:
    enabled: true
    http:
      client:
        requests:
          name: "http.client.requests"
      server:
        requests:
          name: "http.server.requests"

logging:
  level:
    # 默认日志级别
    root: INFO
    # 应用日志
    com.example.captain: INFO
    # 追踪相关日志
    io.micrometer.tracing: INFO
    io.micrometer.observation: INFO
    brave: INFO
    # 降低 Eureka 客户端日志级别
    com.netflix.discovery: WARN
    com.netflix.eureka: WARN
    org.springframework.cloud.netflix.eureka: WARN
    # MyBatis SQL日志
    com.example.captain.mapper: DEBUG
  pattern:
    # 添加traceId和spanId到日志格式，并突出重要信息
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%-5level) %clr([%X{traceId:-}/%X{spanId:-}]){blue} %clr([%thread]){magenta} %clr(%-40.40logger{39}){cyan} : %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{traceId:-}/%X{spanId:-}] [%thread] %-40.40logger{39} : %msg%n"
  file:
    name: /home/<USER>/workspace/microservices-demo/logs/captain-service.log
