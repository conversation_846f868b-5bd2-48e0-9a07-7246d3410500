package com.example.captain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.captain.dto.ProductDTO;
import com.example.captain.dto.ProductQueryParam;
import com.example.captain.entity.Product;
import com.example.captain.mapper.ProductMapper;
import com.example.captain.service.ProductService;
import com.example.captain.util.AsinCompressor;
import com.example.common.exception.BusinessException;
import com.example.common.model.ErrorCodeEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 产品服务实现类
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    private final ProductMapper productMapper;
    private final ObjectMapper objectMapper;

    @Autowired
    public ProductServiceImpl(ProductMapper productMapper, ObjectMapper objectMapper) {
        this.productMapper = productMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveProducts(Long eventId, List<Map<String, Object>> productInfoList) {
        int count = 0;

        for (Map<String, Object> productInfo : productInfoList) {
            try {
                String asin = (String) productInfo.get("ASIN");
                if (asin == null) {
                    log.warn("产品缺少ASIN，跳过保存");
                    continue;
                }

                // 检查是否已存在相同eventId和ASIN的产品
                Product existingProduct = productMapper.findByEventIdAndAsin(eventId, asin);
                if (existingProduct != null) {
                    log.info("产品已存在，跳过保存: eventId={}, ASIN={}", eventId, asin);
                    continue;
                }

                Product product = new Product();
                product.setEventId(eventId);
                product.setAsin(asin);
                String parentAsin = (String) productInfo.get("parentAsin");
                product.setParentAsin(parentAsin);
                product.setIsParentVariant((Boolean) productInfo.get("is_parent_variant"));

                // 设置是否为主商品
                boolean isMainProduct = false;
                if (parentAsin == null) {
                    isMainProduct = true;
                } else if (parentAsin.equals(asin)) {
                    isMainProduct = true;
                }
                product.setIsMainProduct(isMainProduct);
                product.setProductLink((String) productInfo.get("productLink"));
                product.setTitle((String) productInfo.get("title"));
                product.setPrice((String) productInfo.get("price"));
                product.setProductType((String) productInfo.get("Product_type"));
                product.setCountry((String) productInfo.get("country"));
                product.setFeature((String) productInfo.get("feature"));
                product.setDescription((String) productInfo.get("description"));

                // 设置recommendedBrowseNodes字段
                product.setRecommendedBrowseNodes((String) productInfo.get("recommended_browse_nodes"));

                // 设置新字段：产品详细信息和产品概览
                product.setProdDetails((String) productInfo.get("prod_details"));
                product.setProductOverview((String) productInfo.get("product_overview"));

                // 处理图片列表
                if (productInfo.get("images") instanceof List) {
                    product.setImages(objectMapper.writeValueAsString(productInfo.get("images")));
                }

                // 处理变体属性
                if (productInfo.get("variant_attributes") instanceof Map) {
                    product.setVariantAttributes(objectMapper.writeValueAsString(productInfo.get("variant_attributes")));
                }

                // 设置产品SKU
                if (productInfo.get("variant_attributes") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> variantAttributes = (Map<String, Object>) productInfo.get("variant_attributes");

                    // 使用压缩后的parentAsin + 处理过的product_sku
                    String compressedParentAsin = AsinCompressor.compress(parentAsin);

                    // 获取原始product_sku并处理非数字和非字母字符
                    String originalSku = (String) variantAttributes.get("product_sku");
                    String processedSku = "";
                    if (originalSku != null) {
                        // 将非数字和非字母字符替换为下划线
                        processedSku = originalSku.trim().replaceAll("[^0-9a-zA-Z]", "-");
                    }

                    // 组合成最终的SKU
                    String finalSku = compressedParentAsin +"-"+ processedSku;
                    product.setProductSku(finalSku);
                    
                } else {
                    // 如果没有变体属性，直接使用压缩后的ASIN
                    product.setProductSku(AsinCompressor.compress(asin));
                }

                // 保存原始数据
                product.setRawData(objectMapper.writeValueAsString(productInfo));

                // 设置时间
                LocalDateTime now = LocalDateTime.now();
                product.setCreatedAt(now);
                product.setUpdatedAt(now);

                productMapper.insert(product);
                count++;

                log.info("成功保存产品: eventId={}, ASIN={}", eventId, asin);
            } catch (Exception e) {
                log.error("保存产品时出错: {}", e.getMessage(), e);
            }
        }

        return count;
    }

    @Override
    public List<Product> getProductsByEventId(Long eventId) {
        return productMapper.findByEventId(eventId);
    }

    @Override
    public IPage<ProductDTO> getProductPage(ProductQueryParam param) {
        log.info("分页查询产品列表: param={}", param);

        // 创建分页对象
        Page<Product> page = new Page<>(param.getPage(), param.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();

        // 添加事件ID过滤条件
        if (param.getEventId() != null) {
            queryWrapper.eq(Product::getEventId, param.getEventId());
        }

        // 添加父ASIN过滤条件
        if (param.getParentAsin() != null && !param.getParentAsin().isEmpty()) {
            queryWrapper.eq(Product::getParentAsin, param.getParentAsin());
        }

        // 添加是否为主商品过滤条件
        if (param.getIsMainProduct() != null) {
            queryWrapper.eq(Product::getIsMainProduct, param.getIsMainProduct());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(Product::getId);

        // 执行查询
        IPage<Product> productPage = productMapper.selectPage(page, queryWrapper);

        // 转换为DTO对象
        IPage<ProductDTO> dtoPage = productPage.convert(this::convertToDTO);

        return dtoPage;
    }

    @Override
    public ProductDTO getProductDTOById(Long id) {
        log.info("根据ID获取产品详情: id={}", id);

        Product product = productMapper.selectById(id);
        if (product == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "产品不存在");
        }

        return convertToDTO(product);
    }

    @Override
    public ProductDTO getProductDTOByAsin(String asin) {
        log.info("根据ASIN获取产品详情: asin={}", asin);

        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getAsin, asin);

        Product product = productMapper.selectOne(queryWrapper);
        if (product == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "产品不存在");
        }

        return convertToDTO(product);
    }

    @Override
    public Product getProductById(Long id) {
        log.info("根据ID获取产品实体: id={}", id);

        Product product = productMapper.selectById(id);
        if (product == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "产品不存在");
        }

        return product;
    }

    @Override
    public List<Product> getProductsByIds(List<Long> ids) {
        log.info("根据ID列表获取产品列表: ids={}", ids);

        if (ids == null || ids.isEmpty()) {
            return List.of();
        }

        return productMapper.selectBatchIds(ids);
    }

    /**
     * 将实体对象转换为DTO对象
     */
    private ProductDTO convertToDTO(Product product) {
        ProductDTO dto = new ProductDTO();
        BeanUtils.copyProperties(product, dto);
        return dto;
    }
}
