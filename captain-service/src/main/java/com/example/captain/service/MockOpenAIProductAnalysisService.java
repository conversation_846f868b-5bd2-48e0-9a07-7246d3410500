package com.example.captain.service;

import com.example.captain.model.ProductDimensionsAndWeight;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模拟OpenAI商品分析服务
 * 用于在网络不可用时测试Function Call功能
 * 使用正则表达式模拟OpenAI的提取能力
 */
@Service
@Slf4j
public class MockOpenAIProductAnalysisService {

    // 重量匹配模式
    private static final Pattern WEIGHT_PATTERN = Pattern.compile(
            "(?i)(?:weight|重量|净重|毛重)[：:]*\\s*([0-9.]+)\\s*(kg|g|gram|grams|kilogram|kilograms|克|公斤)",
            Pattern.CASE_INSENSITIVE
    );
    
    // 尺寸匹配模式
    private static final Pattern DIMENSION_PATTERN = Pattern.compile(
            "(?i)(?:dimension|size|尺寸|规格)[：:]*\\s*([0-9.]+)\\s*(?:cm|mm|centimeter|厘米|毫米)?\\s*[x×*]\\s*([0-9.]+)\\s*(?:cm|mm|centimeter|厘米|毫米)?\\s*[x×*]\\s*([0-9.]+)\\s*(?:cm|mm|centimeter|厘米|毫米)?",
            Pattern.CASE_INSENSITIVE
    );
    
    // 单独的长宽高匹配
    private static final Pattern LENGTH_PATTERN = Pattern.compile(
            "(?i)(?:length|长度|长)[：:]*\\s*([0-9.]+)\\s*(cm|mm|centimeter|厘米|毫米)?",
            Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern WIDTH_PATTERN = Pattern.compile(
            "(?i)(?:width|宽度|宽)[：:]*\\s*([0-9.]+)\\s*(cm|mm|centimeter|厘米|毫米)?",
            Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern HEIGHT_PATTERN = Pattern.compile(
            "(?i)(?:height|高度|高)[：:]*\\s*([0-9.]+)\\s*(cm|mm|centimeter|厘米|毫米)?",
            Pattern.CASE_INSENSITIVE
    );

    /**
     * 模拟从商品信息中提取重量和尺寸数据
     * 
     * @param title 商品标题
     * @param feature 商品特性
     * @param description 商品描述
     * @return 提取的重量和尺寸信息
     */
    public ProductDimensionsAndWeight extractDimensionsAndWeight(String title, String feature, String description) {
        log.info("开始使用模拟服务提取商品重量和尺寸信息");
        
        try {
            // 合并所有文本进行分析
            String combinedText = combineText(title, feature, description);
            log.info("合并文本: {}", combinedText);
            
            // 提取重量
            WeightInfo weightInfo = extractWeight(combinedText);
            
            // 提取尺寸
            DimensionInfo dimensionInfo = extractDimensions(combinedText);
            
            // 构建结果
            ProductDimensionsAndWeight result = ProductDimensionsAndWeight.builder()
                    .shippingWeight(weightInfo.weight)
                    .shippingWeightUnitOfMeasure(weightInfo.unit)
                    .itemDepthFrontToBack(dimensionInfo.length)
                    .itemDepthUnit(dimensionInfo.lengthUnit)
                    .itemWidthSideToSide(dimensionInfo.width)
                    .itemWidthUnit(dimensionInfo.widthUnit)
                    .itemHeightFloorToTop(dimensionInfo.height)
                    .itemHeightUnitOfMeasure(dimensionInfo.heightUnit)
                    .build();
            
            log.info("模拟提取结果: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("模拟提取过程中出错: {}", e.getMessage(), e);
            return null;
        }
    }
    
    private String combineText(String title, String feature, String description) {
        StringBuilder combined = new StringBuilder();
        if (StringUtils.hasText(title)) {
            combined.append(title).append(" ");
        }
        if (StringUtils.hasText(feature)) {
            combined.append(feature).append(" ");
        }
        if (StringUtils.hasText(description)) {
            combined.append(description);
        }
        return combined.toString();
    }
    
    private WeightInfo extractWeight(String text) {
        Matcher matcher = WEIGHT_PATTERN.matcher(text);
        
        if (matcher.find()) {
            try {
                BigDecimal weight = new BigDecimal(matcher.group(1));
                String unit = matcher.group(2).toLowerCase();
                
                // 转换为KG
                if (unit.contains("g") && !unit.contains("kg")) {
                    // 克转千克
                    weight = weight.divide(new BigDecimal("1000"));
                }
                
                return new WeightInfo(weight, "KG");
            } catch (NumberFormatException e) {
                log.warn("无法解析重量数值: {}", matcher.group(1));
            }
        }
        
        return new WeightInfo(null, null);
    }
    
    private DimensionInfo extractDimensions(String text) {
        DimensionInfo info = new DimensionInfo();
        
        // 尝试匹配完整的三维尺寸
        Matcher dimensionMatcher = DIMENSION_PATTERN.matcher(text);
        if (dimensionMatcher.find()) {
            try {
                info.length = new BigDecimal(dimensionMatcher.group(1));
                info.width = new BigDecimal(dimensionMatcher.group(2));
                info.height = new BigDecimal(dimensionMatcher.group(3));
                info.lengthUnit = "CM";
                info.widthUnit = "CM";
                info.heightUnit = "CM";
                return info;
            } catch (NumberFormatException e) {
                log.warn("无法解析尺寸数值");
            }
        }
        
        // 分别匹配长宽高
        Matcher lengthMatcher = LENGTH_PATTERN.matcher(text);
        if (lengthMatcher.find()) {
            try {
                info.length = new BigDecimal(lengthMatcher.group(1));
                info.lengthUnit = "CM";
            } catch (NumberFormatException e) {
                log.warn("无法解析长度数值: {}", lengthMatcher.group(1));
            }
        }
        
        Matcher widthMatcher = WIDTH_PATTERN.matcher(text);
        if (widthMatcher.find()) {
            try {
                info.width = new BigDecimal(widthMatcher.group(1));
                info.widthUnit = "CM";
            } catch (NumberFormatException e) {
                log.warn("无法解析宽度数值: {}", widthMatcher.group(1));
            }
        }
        
        Matcher heightMatcher = HEIGHT_PATTERN.matcher(text);
        if (heightMatcher.find()) {
            try {
                info.height = new BigDecimal(heightMatcher.group(1));
                info.heightUnit = "CM";
            } catch (NumberFormatException e) {
                log.warn("无法解析高度数值: {}", heightMatcher.group(1));
            }
        }
        
        return info;
    }
    
    private static class WeightInfo {
        BigDecimal weight;
        String unit;
        
        WeightInfo(BigDecimal weight, String unit) {
            this.weight = weight;
            this.unit = unit;
        }
    }
    
    private static class DimensionInfo {
        BigDecimal length;
        String lengthUnit;
        BigDecimal width;
        String widthUnit;
        BigDecimal height;
        String heightUnit;
    }
}
