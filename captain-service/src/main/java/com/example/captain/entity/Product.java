package com.example.captain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 产品实体类
 * 用于存储从Amazon抓取的商品数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "products", autoResultMap = true)
public class Product {

    /**
     * 产品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的事件ID
     */
    @TableField("event_id")
    private Long eventId;

    /**
     * Amazon标准识别号
     */
    @TableField("asin")
    private String asin;

    /**
     * 父商品ASIN
     */
    @TableField("parent_asin")
    private String parentAsin;

    /**
     * 是否为父变体商品
     */
    @TableField("is_parent_variant")
    private Boolean isParentVariant;

    /**
     * 是否为主商品
     */
    @TableField("is_main_product")
    private Boolean isMainProduct;

    /**
     * 商品链接
     */
    @TableField("product_link")
    private String productLink;

    /**
     * 商品标题
     */
    @TableField("title")
    private String title;

    /**
     * 商品价格
     */
    @TableField("price")
    private String price;

    /**
     * 商品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 商品短描述
     */
    @TableField("feature")
    private String feature;

    /**
     * 商品长描述
     */
    @TableField("description")
    private String description;

    /**
     * 图片URL列表，JSON格式
     */
    @TableField("images")
    private String images;

    /**
     * 变体属性，JSON格式
     */
    @TableField("variant_attributes")
    private String variantAttributes;

    /**
     * 商品SKU
     */
    @TableField("product_sku")
    private String productSku;

    /**
     * 原始JSON数据
     */
    @TableField("raw_data")
    private String rawData;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * Amazon推荐浏览节点ID
     */
    @TableField("recommended_browse_nodes")
    private String recommendedBrowseNodes;

    /**
     * 产品详细信息
     */
    @TableField("prod_details")
    private String prodDetails;

    /**
     * 产品概览信息
     */
    @TableField("product_overview")
    private String productOverview;
}
