# OpenAI商品信息提取功能

## 功能概述

本功能使用OpenAI的Function Call能力从商品的title、feature、description中自动提取：
- **运输重量**：以KG为单位
- **包装尺寸**：长、宽、高，以CM为单位

## 工作流程

1. **数据完整性检查**：在保存products数据时，首先检查title、feature、description字段是否都有内容
2. **信息提取**：如果数据完整，则调用OpenAI API提取重量和尺寸信息
3. **数据保存**：将提取的信息保存到products表的相应字段中

## 配置要求

### 1. 环境变量配置
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 2. 应用配置
在`application.yml`中已配置：
```yaml
openai:
  api-key: ${OPENAI_API_KEY:}  # 从环境变量获取
  model: gpt-3.5-turbo         # 使用的模型
  timeout: 30                  # 超时时间（秒）
```

## 数据库结构

### 新增字段（products表）
```sql
-- 运输重量和单位
shipping_weight DECIMAL(8,3) COMMENT '运输重量'
shipping_weight_unit VARCHAR(10) COMMENT '运输重量单位'

-- 包装尺寸和单位
item_depth_front_to_back DECIMAL(8,3) COMMENT '商品深度(前后)'
item_depth_unit VARCHAR(10) COMMENT '深度单位'
item_width_side_to_side DECIMAL(8,3) COMMENT '商品宽度(左右)'
item_width_unit VARCHAR(10) COMMENT '宽度单位'
item_height_floor_to_top DECIMAL(8,3) COMMENT '商品高度(底部到顶部)'
item_height_unit VARCHAR(10) COMMENT '高度单位'
```

## 核心组件

### 1. ProductDimensionsAndWeight
数据模型类，用于存储提取的重量和尺寸信息。

### 2. OpenAIProductAnalysisService
核心服务类，负责：
- 构建OpenAI Function Call请求
- 处理API响应
- 数据格式转换和验证

### 3. ProductServiceImpl
在`saveProducts`方法中集成了：
- 数据完整性检查
- OpenAI信息提取
- 数据库保存

## 使用示例

### 输入数据示例
```json
{
  "title": "Wireless Bluetooth Headphones - 40H Playtime, Weight: 0.5kg",
  "feature": "【40H Playtime】Weight: 500g, Dimensions: 20cm x 15cm x 8cm",
  "description": "Premium headphones. Package: 20x15x8 cm, Weight: 0.5 kg"
}
```

### 提取结果示例
```json
{
  "shippingWeight": 0.5,
  "shippingWeightUnitOfMeasure": "KG",
  "itemDepthFrontToBack": 20.0,
  "itemDepthUnit": "CM",
  "itemWidthSideToSide": 15.0,
  "itemWidthUnit": "CM",
  "itemHeightFloorToTop": 8.0,
  "itemHeightUnitOfMeasure": "CM"
}
```

## 错误处理

1. **API密钥未配置**：服务会记录警告并跳过OpenAI提取
2. **数据不完整**：跳过保存并记录警告日志
3. **API调用失败**：记录错误日志，继续保存其他产品信息
4. **提取结果无效**：记录信息日志，不影响产品保存

## 日志示例

```
2024-01-01 10:00:00.000 INFO  [trace/span] [main] ProductServiceImpl : 开始使用OpenAI提取商品重量和尺寸信息
2024-01-01 10:00:01.000 INFO  [trace/span] [main] ProductServiceImpl : 成功提取商品重量和尺寸信息: ASIN=B08XYZ123, 数据=ProductDimensionsAndWeight(...)
2024-01-01 10:00:02.000 WARN  [trace/span] [main] ProductServiceImpl : 产品信息不完整，跳过保存: ASIN=B08ABC456, title=有, feature=无, description=有
```

## 测试

运行测试前需要配置有效的OpenAI API密钥：
```bash
# 设置环境变量
export OPENAI_API_KEY="your-api-key"

# 运行测试
mvn test -Dtest=OpenAIProductAnalysisServiceTest
```

## 性能考虑

1. **API调用成本**：每次提取会消耗OpenAI API配额
2. **响应时间**：OpenAI API调用可能需要几秒钟
3. **并发限制**：注意OpenAI API的并发限制
4. **重试机制**：当前未实现重试，可根据需要添加

## 扩展建议

1. **缓存机制**：对相同的商品信息缓存提取结果
2. **批量处理**：支持批量提取以提高效率
3. **模型优化**：根据实际效果调整提示词和模型参数
4. **多语言支持**：支持不同语言的商品信息提取
